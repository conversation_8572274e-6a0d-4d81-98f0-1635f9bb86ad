#!/usr/bin/env python3
"""
Test the Excel export functionality with the specific email
"""

import os
import tempfile
from email_processor import EmailParser
import openpyxl
from openpyxl.styles import Font, Alignment

def test_excel_export():
    """Test Excel export with the specific email"""
    
    email_path = r"C:\Users\<USER>\Desktop\eml\test_emails\100_您收到一张【青岛喜晟达餐饮中心（有限合伙）】开具的数电发票【发票号码：25924000000002681078】，请查收.eml"
    
    print("=== Testing Excel Export ===")
    print(f"File: {os.path.basename(email_path)}")
    print()
    
    # Initialize parser
    parser = EmailParser()
    
    try:
        # Parse the email file
        result = parser.parse_eml_file(email_path)
        
        print(f"Parsed body length: {len(result['body'])}")
        print(f"Body content preview: {result['body'][:100]}...")
        print()
        
        # Create Excel file manually (simulating the export process)
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Email Processing Results"
        
        # Define headers (simplified for testing)
        headers = [
            'Filename', 'Subject', 'From Email', 'From Name', 'To Emails', 'Date',
            'Body Content', 'Has HTML', 'Content Type', 'Error'
        ]
        
        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # Write data
        row_data = [
            result['filename'],
            result['subject'],
            result['from_email'],
            result['from_name'],
            result['to_emails'],
            result['date'],
            result['body'],  # This is the critical field
            result['has_html'],
            result['content_type'],
            result['error']
        ]
        
        for col, value in enumerate(row_data, 1):
            cell = ws.cell(row=2, column=col, value=value)
            # Set text wrapping for body content
            if col == 7:  # Body Content column
                cell.alignment = Alignment(wrap_text=True, vertical='top')
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)  # Cap at 50 characters
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_path = temp_file.name
        
        wb.save(temp_path)
        
        print(f"Excel file saved to: {temp_path}")
        
        # Read back the Excel file to verify content
        print("\n=== Verifying Excel Content ===")
        wb_read = openpyxl.load_workbook(temp_path)
        ws_read = wb_read.active
        
        # Get the body content cell
        body_cell = ws_read.cell(row=2, column=7)
        body_value = body_cell.value
        
        print(f"Body content in Excel (length: {len(str(body_value)) if body_value else 0}):")
        print(f"'{body_value}'")
        
        if body_value and str(body_value).strip():
            print("✅ SUCCESS: Body content is properly stored in Excel!")
            
            # Check for Chinese characters
            chinese_chars = [char for char in str(body_value) if '\u4e00' <= char <= '\u9fff']
            print(f"✅ Found {len(chinese_chars)} Chinese characters in Excel")
            
        else:
            print("❌ ISSUE: Body content is empty in Excel!")
        
        # Clean up
        try:
            os.unlink(temp_path)
        except:
            pass
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_excel_export()
