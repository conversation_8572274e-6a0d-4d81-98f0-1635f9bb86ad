Return-Path: <<EMAIL>>
Delivered-To: <EMAIL>
Received: from smtp.example.com (smtp.example.com [203.0.113.1])
	by mx.example.com (Postfix) with ESMTP id ABCDEF123456
	for <<EMAIL>>; Wed, 17 Jan 2024 16:45:00 +0800 (CST)
Message-ID: <<EMAIL>>
Date: Wed, 17 Jan 2024 16:45:00 +0800
From: =?UTF-8?B?55So5oi3?= <<EMAIL>>
To: =?UTF-8?B?5pS25Lu25Lq6?= <<EMAIL>>
Subject: =?UTF-8?B?566A5Y2V6YKu5Lu2?=
MIME-Version: 1.0
Content-Type: text/plain; charset="UTF-8"
Content-Transfer-Encoding: 8bit

你好！

这是一封简单的测试邮件。

内容包含中文字符，用于测试邮件处理程序的中文编码支持。

祝好！

用户
