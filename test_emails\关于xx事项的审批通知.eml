Return-Path: <<EMAIL>>
Delivered-To: <EMAIL>
Received: from mail.company.com (mail.company.com [*************])
	by mx.company.com (Postfix) with ESMTP id 12345ABCDE
	for <<EMAIL>>; Mon, 15 Jan 2024 10:30:00 +0800 (CST)
Message-ID: <<EMAIL>>
Date: Mon, 15 Jan 2024 10:30:00 +0800
From: =?UTF-8?B?5byg5LiJ?= <<EMAIL>>
To: =?UTF-8?B?5p2O5Zub?= <<EMAIL>>, =?UTF-8?B?546L4LiU?= <<EMAIL>>
Cc: =?UTF-8?B?6LW15LiJ?= <<EMAIL>>
Subject: =?UTF-8?B?5YWz5LqO5LiA6aG555uu55qE5a6h5om55YCa6K6u?=
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="----=_NextPart_000_0001_01DA1234.56789ABC"
X-Priority: 1
Importance: High

This is a multi-part message in MIME format.

------=_NextPart_000_0001_01DA1234.56789ABC
Content-Type: text/plain; charset="UTF-8"
Content-Transfer-Encoding: 8bit

尊敬的各位同事：

关于XX项目的审批通知如下：

1. 项目名称：XX系统升级改造项目
2. 申请部门：信息技术部
3. 预算金额：人民币50万元
4. 实施周期：2024年2月-2024年6月

经过公司领导层审议，该项目已获得批准。请相关部门按照既定计划推进项目实施。

如有疑问，请及时联系项目负责人。

此致
敬礼！

张三
信息技术部经理
电话：010-12345678
邮箱：<EMAIL>

------=_NextPart_000_0001_01DA1234.56789ABC
Content-Type: application/pdf; name="=?UTF-8?B?6aG555uu5paH5Lu2LnBkZg==?="
Content-Transfer-Encoding: base64
Content-Disposition: attachment; filename="=?UTF-8?B?6aG555uu5paH5Lu2LnBkZg==?="

JVBERi0xLjQKJcOkw7zDtsO4DQoxIDAgb2JqCjw8Ci9UeXBlIC9DYXRhbG9nCi9QYWdlcyAyIDAg
UgovTGFuZyAoemgtQ04pCj4+CmVuZG9iago=

------=_NextPart_000_0001_01DA1234.56789ABC--
