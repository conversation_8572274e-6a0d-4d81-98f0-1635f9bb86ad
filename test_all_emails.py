#!/usr/bin/env python3
"""
Test all emails in the test folder to identify any remaining issues
"""

import os
import glob
from email_processor import <PERSON>ailParser

def test_all_emails():
    """Test all emails in the test folder"""
    
    test_folder = r"C:\Users\<USER>\Desktop\eml\test_emails"
    
    print("=== Testing All Emails in Test Folder ===")
    print(f"Folder: {test_folder}")
    print()
    
    # Get all .eml files
    eml_files = glob.glob(os.path.join(test_folder, "*.eml"))
    
    if not eml_files:
        print("No .eml files found in test folder")
        return
    
    print(f"Found {len(eml_files)} email files:")
    for file in eml_files:
        print(f"  - {os.path.basename(file)}")
    print()
    
    # Initialize parser
    parser = EmailParser()
    
    # Test each email
    issues_found = []
    
    for email_path in eml_files:
        filename = os.path.basename(email_path)
        print(f"=== Testing: {filename} ===")
        
        try:
            # Parse the email
            result = parser.parse_eml_file(email_path)
            
            print(f"Subject: {result['subject']}")
            print(f"From: {result['from_full']}")
            print(f"Content Type: {result['content_type']}")
            print(f"Has HTML: {result['has_html']}")
            print(f"Body Length: {len(result['body'])}")
            print(f"Error: {result['error']}")
            
            # Check for issues
            has_issue = False
            
            # Check if body is empty when it shouldn't be
            if not result['body'] or not result['body'].strip():
                if not result['error']:  # Only flag as issue if there's no parsing error
                    print("❌ ISSUE: Body content is empty!")
                    has_issue = True
                else:
                    print(f"⚠️  Body empty due to error: {result['error']}")
            else:
                print("✅ Body content extracted successfully")
                
                # Show preview of body content
                preview = result['body'][:100].replace('\n', ' ')
                print(f"   Preview: {preview}...")
            
            # Check for Chinese content if filename suggests it
            if any(ord(char) > 127 for char in filename):  # Non-ASCII characters in filename
                chinese_chars = [char for char in result['body'] if '\u4e00' <= char <= '\u9fff']
                if chinese_chars:
                    print(f"✅ Found {len(chinese_chars)} Chinese characters")
                else:
                    if result['body'] and result['body'].strip():
                        print("⚠️  No Chinese characters found (might be expected)")
                    
            if has_issue:
                issues_found.append({
                    'file': filename,
                    'issue': 'Empty body content',
                    'result': result
                })
            
        except Exception as e:
            print(f"❌ ERROR parsing {filename}: {e}")
            issues_found.append({
                'file': filename,
                'issue': f'Parsing error: {e}',
                'result': None
            })
        
        print("-" * 60)
        print()
    
    # Summary
    print("=== SUMMARY ===")
    if issues_found:
        print(f"❌ Found {len(issues_found)} issues:")
        for issue in issues_found:
            print(f"  - {issue['file']}: {issue['issue']}")
    else:
        print("✅ All emails processed successfully!")
    
    return issues_found

if __name__ == "__main__":
    issues = test_all_emails()
    if issues:
        print(f"\n💥 {len(issues)} issues need to be addressed.")
    else:
        print("\n🎉 All emails are processing correctly!")
